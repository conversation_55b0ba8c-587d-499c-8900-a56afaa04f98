//External
import { MdAddCircle, MdAnalytics, MdViewList } from "react-icons/md";

export const ConfigBackendRoute = {
  route_activity_all: "/merchant/activity/all",
  route_history_current: "/merchant/history/current",
  route_history_custom: "/merchant/history/custom",
  route_order_step: "/create-transactions",
};

export const ConfigHomeCategory = {
  data: {
    dashboard: {
      icon: <MdAnalytics color="black" size={25} />,
      header: "Sales Dashboard",
      text: "Dashboard",
    },
    order: {
      icon: <MdAddCircle color="black" size={25} />,
      header: "New Order",
      text: "New Order",
    },
    activity: {
      icon: <MdViewList color="black" size={25} />,
      header: "Payment Activity",
      text: "Activity",
    },
  },
  default_key: 0,
};

export const ConfigMerchantOrder = {
  order_limit: 10000, //denotes $100.00
};
