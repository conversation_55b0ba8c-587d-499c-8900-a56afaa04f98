//Standard
import React , { useContext , useEffect, useState } from "react";

//External
import { Button , Container , Image , Modal , Row , Stack , Table } from "react-bootstrap";
import { RxCross2 } from "react-icons/rx"

//Design
import "../style.css"

//Asset
import order from "../asset/image/order.png"
import orderFail from "../asset/gif/order_fail.gif"
import orderPending from "../asset/gif/order_pending.gif"
import orderSuccess from "../asset/gif/order_success.gif"

//External
import axios from "axios"
import { HiArrowLongRight } from "react-icons/hi2"
import { PiWarningCircle } from "react-icons/pi"

//Internal 
import { ConfigBackendRoute , ConfigMerchantOrder } from "../config";
import { MerchantContext } from "../merchant_context";
import { GetCurrentDate , GetEpochMilliseconds , GetCurrentTime, GetIsoTimeNow } from "../utility/epoch";
import { ConvertAmountFloat, ConvertAmountInteger } from "../utility/number";

export default function ContentOrder( props )
{
	//Context
	const merchantContext = useContext( MerchantContext )

	//State
	const [orderAmount , setOrderAmount] = useState( 0 )
	const [orderAmountText , setOrderAmountText] = useState( '' )
	const [orderStep , setOrderStep] = useState( 1 )	//1 = input, 2 = confirm, 3 = processing, 4 = complete
	const [orderStatus , setOrderStatus] = useState( false )	//false = failed, true = successful

	useEffect
	(	() =>
		{
			//Refresh data after order completed
			if ( 4 === orderStep )
			{	
				//Refresh activity
				merchantContext.RefreshActivityData()

				//Refresh dashboard
				//note: slight delay to ensure stripe webhook has processed
				setTimeout( () => { merchantContext.RefreshDashboardCurrent() } , 3000 )
			}//end if
		}	
	,	[orderStep, merchantContext]	
	)

	const RenderInputAmount = () =>
	{
		//Return element
		return (
			<>
				<label id="order-input-amount-stack-header">Order Amount</label>
				<Stack 
					className="px-4" 
					direction="horizontal" 
					id={ ConfigMerchantOrder.order_limit < orderAmount ? "order-input-amount-stack-warning" : "order-input-amount-stack"}
					gap={5}
				>
					<label>$</label>
					{	'' === orderAmountText
					?	<label id="order-input-amount-stack-value-none">0.00</label>
					:	<label>{orderAmountText}</label>
					}
				</Stack>
				<Stack direction="horizontal" gap={4}>									
					{	ConfigMerchantOrder.order_limit < orderAmount
					?	<>
							<PiWarningCircle color="red" size={20} />
							<label id="order-input-amount-stack-warning-active">Value exceeded transaction limit of $100.00</label>
						</>
					:	<>
							<PiWarningCircle color="gray" size={20} />
							<label id="order-input-amount-stack-warning-none">Transaction limit of $100.00 applies</label>
						</>
					}
				</Stack>
			</>
		)
	}

	const RenderInputNumpad = () => 
	{
		//Return element
		return (
			<>	
				<Table borderless id="order-input-table-calculator">
					<tbody>
						<tr>
							<td>
								<Button 
									id="order-input-table-calculator-button-number" 
									onClick={(event) => HandleInputNumpad(event)} 
									value={1}
								>
									1
								</Button>
							</td>
							<td>
								<Button 
									id="order-input-table-calculator-button-number" 
									onClick={(event) => HandleInputNumpad(event)} 
									value={2}
								>
									2
								</Button>
							</td>
							<td>
								<Button 
									id="order-input-table-calculator-button-number" 
									onClick={(event) => HandleInputNumpad(event)} 
									value={3}
								>
									3
								</Button>
							</td>
							<td>
								<Button 
									id="order-input-table-calculator-button-clear" 
									onClick={HandleInputClear}
								>
									AC
								</Button>
							</td>
						</tr>
						<tr>
							<td>
								<Button 
									id="order-input-table-calculator-button-number" 
									onClick={(event) => HandleInputNumpad(event)} 
									value={4}
								>
									4
								</Button>
							</td>
							<td>
								<Button 
									id="order-input-table-calculator-button-number" 
									onClick={(event) => HandleInputNumpad(event)} 
									value={5}
								>
									5
								</Button>
							</td>
							<td>
								<Button 
									id="order-input-table-calculator-button-number" 
									onClick={(event) => HandleInputNumpad(event)} 
									value={6}
								>
									6
								</Button>
							</td>
							<td rowSpan={3}>
								{	(	0 === orderAmount 
									||	ConfigMerchantOrder.order_limit < orderAmount
									)
								?	<Button 
										disabled
										id="order-input-table-calculator-button-submit" 
									>
										<HiArrowLongRight color="white" size={24} />
									</Button>
								:	<Button 
										id="order-input-table-calculator-button-submit" 
										onClick={() => setOrderStep( previous => previous + 1 )}
									>
										<HiArrowLongRight color="white" size={24} />
									</Button>
								}
							</td>
						</tr>
						<tr>
							<td>
								<Button 
									id="order-input-table-calculator-button-number" 
									onClick={(event) => HandleInputNumpad(event)} 
									value={7}
								>
									7
								</Button>
							</td>
							<td>
								<Button 
									id="order-input-table-calculator-button-number" 
									onClick={(event) => HandleInputNumpad(event)} 
									value={8}
								>
									8
								</Button>
							</td>
							<td>
								<Button 
									id="order-input-table-calculator-button-number" 
									onClick={(event) => HandleInputNumpad(event)} 
									value={9}
								>
									9
								</Button>
							</td>
						</tr>
						<tr>
							<td colSpan={2}>
								<Button 
									id="order-input-table-calculator-button-number" 
									onClick={(event) => HandleInputNumpad(event)} 
									value={0}
								>
									0
								</Button>
							</td>
							<td>
								<Button 
									id="order-input-table-calculator-button-number" 
									onClick={(event) => HandleInputNumpad(event)} 
									value="."
								>
									.
								</Button>
							</td>
						</tr>
					</tbody>
				</Table>
			</>
		)
	}

	const RenderModalHeader = ( step ) =>
	{
		//Check order step
		switch( step )
		{
			//Input
			case 1: return 'New Order'

			//Confirm
			case 2: return 'Confirm Payment'

			//Processing
			case 3: return 'Payment Processing'

			//Complete
			case 4: 
				return (
					true === orderStatus
				?	'Payment Successful'
				:	'Payment Failed'
				)

			//Default
			default: return ''
		}
	}

	const RenderOrderPage = ( step ) => 
	{
		//Check order step
		switch( step )
		{
			//Input page
			case 1:
			{
				//Return element
				return (
					<Container className="px-5">
						<Row className="my-3 px-4">
							<Stack direction="vertical" gap={3}>
								<RenderInputAmount />
							</Stack>
						</Row>
						<Row className="px-4">
							<RenderInputNumpad />
						</Row>	
					</Container>
				)
			}//end case
			
			//Confirm page
			case 2:
			{
				return (
					<Stack className="px-5" direction="vertical" gap={5}>
						<div className="align-items-center d-flex justify-content-center">
							<Image id="order-confirm-image" src={order}/>
						</div>
						<Stack className="align-items-center d-flex justify-content-center" direction="vertical" gap={3}>
							<label id="order-header-text">Process payment of</label>
							<Stack className="align-items-center d-flex justify-content-center" direction="horizontal" gap={2}>
								<label id="order-amount-text">$</label>
								<label id="order-amount-text">{ConvertAmountFloat( orderAmount , 2 )}</label>
								<label id="order-amount-text">?</label>
							</Stack>
						</Stack>
						<Stack className="align-items-center d-flex justify-content-center mt-5" direction="vertical" gap={3}>
							<Button id="order-confirm-button-confirm" onClick={HandleConfirmConfirm}>Confirm</Button>
							<Button id="order-confirm-button-cancel" onClick={HandleConfirmCancel}>Cancel</Button>
						</Stack>
					</Stack>
				)
			}//end case
			
			//Processing page
			case 3:
			{
				//Return element
				return (
					<Stack className="px-5" direction="vertical" gap={5}>
						<div className="align-items-center d-flex justify-content-center">
							<Image id="order-status-image" src={orderPending} />
						</div>
						<Stack className="align-items-center d-flex justify-content-center" direction="vertical" gap={3}>
							<label id="order-header-text">Payment processing</label>
							<Stack className="align-items-center d-flex justify-content-center" direction="horizontal" gap={3}>
								<label id="order-amount-text">$</label>
								<label id="order-amount-text">{ConvertAmountFloat( orderAmount , 2 )}</label>
							</Stack>
						</Stack>
					</Stack>
				)
			}//end case

			//Complete page
			case 4:
			{
				//Return element
				return (
					<Stack className="align-items-center d-flex justify-content-center px-5" direction="vertical" gap={5}>
						<div className="align-items-center d-flex justify-content-center">
							{	true === orderStatus
							?	<Image id="order-status-image" src={orderSuccess} />
							:	<Image id="order-status-image" src={orderFail} />
							}
						</div>
						<Stack className="align-items-center d-flex justify-content-center" direction="vertical" gap={3}>
							{	true === orderStatus
							?	<>
									<label id="order-header-text">Payment Successful</label>
									<Stack className="align-items-center d-flex justify-content-center" direction="horizontal" gap={3}>
										<label id="order-amount-text">$</label>
										<label id="order-amount-text">{ConvertAmountFloat( orderAmount , 2 )}</label>
									</Stack>
								</>
							:	<>
									<label id="order-header-text">Payment Failed</label>
									<label id="order-header-subtext">Please use other payment methods instead</label>
								</>
							}
						</Stack>
						<Button className="mt-5" id="order-complete-button-cancel" onClick={HandleModalClose}>Close & Finish</Button>
					</Stack>
				)
			}//end case

			//Default case
			default: return ''
		}//end switch
	}

	function GetOrderId()
	{
		//Return order ID
		return String( GetCurrentDate( '' ) + '-' + GetCurrentTime( '' ) + '-' + GetCurrentDate( '' ) + '_' + GetCurrentTime( '' ) )
	}

	function HandleConfirmConfirm()
	{
		//Set order step
		setOrderStep( previous => previous + 1 )
		
		//Send order
		SendOrderRequest()			
	}

	function HandleConfirmCancel()
	{
		//Reset order amount
		setOrderAmount( '' )

		//Reset order step
		setOrderStep( previous => 1 )
	}

	function HandleInputClear()
	{
		//Reset order amounts
		setOrderAmount( previous => 0 )
		setOrderAmountText( previous => '' )
	}

	function HandleInputNumpad( event )
	{
		//Return if invalid inputs occur
		if ( orderAmountText.includes( '.' ) ) 
		{
			//Prevent additional decimal
			if ( '.' === event.target.value ) return

			//Limit 2 decimals
			let temp_amountText_decimal = orderAmountText.split( '.' )[1]
			if ( 2 <= temp_amountText_decimal.length ) return
		}//end if

		//Limit 3 wholes
		if ( ConfigMerchantOrder.order_limit <= orderAmount ) return

		//Extract variaables
		let temp_amountText_new = orderAmountText + event.target.value

		//Append input
		setOrderAmountText( previous => temp_amountText_new )

		//Parse and set order amount if amount is valid
		if ( false === isNaN( ConvertAmountInteger( temp_amountText_new ) ) ) setOrderAmount( previous => ConvertAmountInteger( temp_amountText_new ) )
	}
	
	function HandleModalClose()
	{
		//Close modal
		props.setTrigger( false )

		//Reset states
		setOrderAmount( 0 )
		setOrderAmountText( '' )
		setOrderStatus( previous => false )
		setOrderStep( previous => 1 )
	}
	
	function SendOrderRequest()
	{
		//Get variables
		let temp_iso_datetime = GetIsoTimeNow()
		let temp_orderId = GetOrderId()

		//Send request
		axios
		.post
		(	process.env.REACT_APP_BACKEND_API_URL + ConfigBackendRoute.route_order_step
		,	{	datetimestamp: temp_iso_datetime
			// ,	building_id: process.env.REACT_APP_TRANSACTION_BUILDINGID
			// ,	counter_id: process.env.REACT_APP_TRANSACTION_COUNTERID
			// ,	level_id: process.env.REACT_APP_TRANSACTION_LEVELID
			,	merchantId: process.env.REACT_APP_TRANSACTION_MERCHANTID
			,	transactionAmount: orderAmountText
			,	transactionId: temp_orderId
			}
		,	{ headers: { 'x-api-key': process.env.REACT_APP_BACKEND_API_KEY } }
		)
		.then
		(	(response) =>
			{	
				const result = JSON.parse(response.data.body)
				//Check status
				if ( true === result.success )
				{
					//Log
					console.log( "Completed payment flow" )

					//Set status
					setOrderStatus( previous => true )

					//Move to status page
					setOrderStep( previous => 4 )
				}
				else
				{
					//Log
					console.log( "Failed payment flow" )

					//Set status
					setOrderStatus( previous => false )

					//Move to status page
					setOrderStep( previous => 4 )
				}//end if statement
			}
		)
		.catch
		(	(error) =>
			{
				//Log
				console.log( error )
				console.log( "Error with payment flow" )

				//Set status
				setOrderStatus( previous => false )

				//Move to complete page
				setOrderStep( previous => 4 )
			}
		)
	}

	//Return element
	return (
		<>
			<Modal fullscreen={true} onHide={() => props.setTrigger(false)} show={props.trigger}>
				<Modal.Header className="mb-5">
					<Stack className="py-2" direction="horizontal" gap={2}>
						{	3 !== orderStep
						?	<Button id="order-modal-header-close" onClick={HandleModalClose}>
								<RxCross2 color="#44474F" size={28} />
							</Button>
						:	''
						}
						<label id="order-modal-header">{RenderModalHeader( orderStep )}</label>
					</Stack>
				</Modal.Header>
				
				<Modal.Body className="px-5">
					{RenderOrderPage( orderStep )}
				</Modal.Body>
			</Modal>
		</>
	)
}