const DayNameLong = [
  "Sunday",
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
];
const MonthNameLong = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];
const MonthNameShort = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
];

export const GetCurrentDate = (delimiter) => {
  //Instantiate date object
  let temp_date = new Date();

  //Return current date
  return (
    temp_date.getFullYear() +
    delimiter +
    String(temp_date.getMonth() + 1).padStart(2, "0") + //javascript returns between 0 to 11
    delimiter +
    String(temp_date.getDate()).padStart(2, "0")
  );
};

export const GetCurrentDateText = () => {
  //Instantiate date object
  let temp_date = new Date();

  //Return current date
  return (
    temp_date.getDate() +
    " " +
    MonthNameLong[temp_date.getMonth()] +
    " " +
    temp_date.getFullYear()
  );
};

export const GetCurrentDayDate = () => {
  //Instantiate date object
  let temp_date = new Date();

  //Return current date
  return (
    DayNameLong[temp_date.getDay()] +
    " " +
    String(temp_date.getDate()).padStart(2, "0") +
    " " +
    MonthNameLong[temp_date.getMonth()] +
    " " +
    temp_date.getFullYear()
  );
};

export const GetCurrentTime = (delimiter) => {
  //Instantiate date object
  let temp_date = new Date();

  return (
    String(temp_date.getHours()).padStart(2, "0") +
    delimiter +
    String(temp_date.getMinutes()).padStart(2, "0") +
    delimiter +
    String(temp_date.getSeconds()).padStart(2, "0")
  );
};

export const GetEpochDateTimeActivity = (epoch) => {
  //Instantiate date object
  let temp_date = new Date(epoch);

  //Variables for time
  let temp_time_hour = 0;
  let temp_time_period = "";

  //Calculate hour
  if (12 > temp_date.getHours()) {
    //Store hour
    temp_time_hour =
      (10 > temp_date.getHours() ? "0" : "") + temp_date.getHours();

    //Store period
    temp_time_period = "AM";
  } else {
    //Store hour
    12 < temp_date.getHours()
      ? (temp_time_hour = temp_date.getHours() - 12)
      : (temp_time_hour = temp_date.getHours());

    //Store period
    temp_time_period = "PM";
  } //end if

  //Return date time for activity
  return (
    temp_date.getDate() +
    " " +
    MonthNameShort[temp_date.getMonth()] +
    " " +
    temp_date.getFullYear() +
    ", " +
    temp_time_hour +
    ":" +
    (10 > temp_date.getMinutes() ? "0" : "") +
    temp_date.getMinutes() +
    " " +
    temp_time_period
  );
};

export const GetEpochMilliseconds = () => {
  //Return epoch in milliseconds
  return Date.now();
};

export const GetIsoTimeNow = () => {
  //Return current date time in ISO format
  return new Date().toISOString();
};

export const GetMinutesSinceMidnight = () => {
  //Instantiate date object
  let temp_date = new Date();

  //Return minutes since midnight
  return parseInt(temp_date.getHours() * 60 + temp_date.getMinutes());
};
